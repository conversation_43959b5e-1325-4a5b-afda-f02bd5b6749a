
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'add03560f1b9f5a2b0455fa1b872740ba0f34e9aeb52c88bfccde8ad158407c0', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '6feabbb52af9a9fc5b3a15d323da7e4db19e23092b0a76391f9afe319bcdcbf4', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-UJ27JA5M.css': {size: 298495, hash: 'KuJzfxHS510', text: () => import('./assets-chunks/styles-UJ27JA5M_css.mjs').then(m => m.default)}
  },
};
