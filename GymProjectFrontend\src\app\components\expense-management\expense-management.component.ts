import { Component, OnInit, OnDestroy, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ExpenseService } from '../../services/expense.service';
import { ExpenseDto } from '../../models/expenseDto.model';
import { ExpenseDashboardDto } from '../../models/expenseDashboardDto.model';
import { ExpensePagingParameters, ExpenseFilterState, ExpenseSortState } from '../../models/expensePagingParameters';
import { PaginatedResult } from '../../models/pagination';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SingleResponseModel } from '../../models/singleResponseModel';
import { faEdit, faTrashAlt, faPlus, faFilter, faCalendarAlt, faSort, faSortUp, faSortDown, faFileExport, faFileExcel, faFilePdf } from '@fortawesome/free-solid-svg-icons';
import { ExpenseDialogComponent } from '../expense-dialog/expense-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, registerables, ChartOptions, TooltipItem, ChartConfiguration } from 'chart.js';
import { ChartUtilsService } from '../../services/chart-utils.service';
import * as XLSX from 'xlsx';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

Chart.register(...registerables);

@Component({
  selector: 'app-expense-management',
  templateUrl:  './expense-management.component.html',
  styleUrls: ['./expense-management.component.css'],
  standalone: false
})
export class ExpenseManagementComponent implements OnInit, OnDestroy, AfterViewInit {

  // Pagination ve filtreleme
  paginatedExpenses: PaginatedResult<ExpenseDto> = {
    data: [],
    pageNumber: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasPrevious: false,
    hasNext: false
  };

  isLoading = false;
  totalDailyExpense: number = 0;
  totalMonthlyExpense: number = 0;
  totalYearlyExpense: number = 0;

  // Filtreleme durumu
  filterState: ExpenseFilterState = {
    searchText: '',
    startDate: null,
    endDate: null,
    expenseType: '',
    minAmount: null,
    maxAmount: null,
    selectedYear: new Date().getFullYear(),
    selectedMonth: new Date().getMonth() + 1
  };

  // Başlangıç filtre durumu (ara butonu için)
  private initialFilterState: ExpenseFilterState;

  // Sıralama durumu
  sortState: ExpenseSortState = {
    sortBy: 'ExpenseDate',
    sortDirection: 'desc'
  };

  // Eski değişkenler (geriye uyumluluk için)
  selectedYear: number;
  selectedMonth: number;
  initialYear: number;
  initialMonth: number;
  years: number[] = [];
  months: { value: number, name: string }[] = [
    { value: 1, name: 'Ocak' }, { value: 2, name: 'Şubat' }, { value: 3, name: 'Mart' },
    { value: 4, name: 'Nisan' }, { value: 5, name: 'Mayıs' }, { value: 6, name: 'Haziran' },
    { value: 7, name: 'Temmuz' }, { value: 8, name: 'Ağustos' }, { value: 9, name: 'Eylül' },
    { value: 10, name: 'Ekim' }, { value: 11, name: 'Kasım' }, { value: 12, name: 'Aralık' }
  ];

  searchControl = new FormControl('');

  // Gider türleri
  expenseTypes: string[] = [
    'Fatura - Elektrik', 'Fatura - Su', 'Fatura - Doğalgaz', 'Fatura - İnternet',
    'Maaş Ödemesi', 'Kira', 'Malzeme Alımı', 'Temizlik Malzemesi',
    'Ofis Gideri', 'Bakım/Onarım', 'Vergi/Harç', 'Diğer'
  ];

  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faPlus = faPlus;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;
  faSort = faSort;
  faSortUp = faSortUp;
  faSortDown = faSortDown;
  faFileExport = faFileExport;
  faFileExcel = faFileExcel;
  faFilePdf = faFilePdf;

  private destroy$ = new Subject<void>();

  // Chart instances
  distributionChart: Chart | null = null;
  trendChart: Chart | null = null;
  monthlyTrendData: { labels: string[], data: number[] } = { labels: [], data: [] }; // Aylık trend verisi için
  // trendChart: Chart | null = null; // Duplicate kaldırıldı
  initialLoadComplete = false;

  // Template'de kullanım için
  Math = Math;

  // Performans iyileştirmeleri
  private filterCache = new Map<string, PaginatedResult<ExpenseDto>>();
  private lastFilterParams: string = '';

  // Loading states
  isSearching = false;
  isExporting = false;

  // Spam koruması
  private lastSearchTime = 0;
  private searchCooldown = 1000; // 1 saniye cooldown
  private lastExportTime = 0;
  private exportCooldown = 3000; // 3 saniye cooldown
  private lastDialogOpenTime = 0;
  private dialogCooldown = 500; // 0.5 saniye cooldown

  // Favori filtreler kaldırıldı

  constructor(
    private expenseService: ExpenseService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private cdRef: ChangeDetectorRef,
    private chartUtils: ChartUtilsService
  ) {
    const currentDate = new Date();
    this.initialYear = currentDate.getFullYear();
    this.initialMonth = currentDate.getMonth() + 1;
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;

    // Filter state'i başlat
    this.filterState.selectedYear = this.initialYear;
    this.filterState.selectedMonth = this.initialMonth;

    // Başlangıç filtre durumunu kaydet
    this.initialFilterState = { ...this.filterState };

    // Dinamik yıl listesi: mevcut yıldan 2 yıl sonrasına kadar, 5 yıl öncesine kadar
    const currentYear = new Date().getFullYear();
    const startYear = currentYear + 2; // 2 yıl sonrası
    const endYear = currentYear - 5;   // 5 yıl öncesi
    this.years = [];
    for (let i = startYear; i >= endYear; i--) {
      this.years.push(i);
    }
  }

  ngOnInit(): void {
    // Arama durumunu sıfırla
    this.isSearching = false;

    // Dashboard verilerini yükle
    this.loadDashboardData();

    // Sayfalanmış verileri yükle
    this.loadExpensesPaginated();

    // Otomatik arama kaldırıldı - sadece ara butonuyla arama yapılacak
    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe((value) => {
      this.filterState.searchText = value || '';
    });

    // Keyboard navigation
    this.setupKeyboardNavigation();

    // Favori filtreler kaldırıldı
  }

  private setupKeyboardNavigation(): void {
    document.addEventListener('keydown', this.handleKeyboardEvent);
  }

  ngAfterViewInit(): void {
    if (this.initialLoadComplete) {
       this.createOrUpdateCharts();
    }
  }


  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroyCharts();

    // Keyboard event listener'ı temizle
    document.removeEventListener('keydown', this.handleKeyboardEvent);
  }

  private handleKeyboardEvent = (event: KeyboardEvent) => {
    // Keyboard navigation logic
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
      event.preventDefault();
      this.openExpenseDialog();
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
      event.preventDefault();
      this.exportToExcel();
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      const searchInput = document.querySelector('.search-input') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    }

    if (event.key === 'Escape') {
      if (this.hasActiveFilters()) {
        this.clearFilters();
      }
    }

    if (event.altKey) {
      if (event.key === 'ArrowLeft' && this.paginatedExpenses.hasPrevious) {
        event.preventDefault();
        this.goToPage(this.paginatedExpenses.pageNumber - 1);
      }
      if (event.key === 'ArrowRight' && this.paginatedExpenses.hasNext) {
        event.preventDefault();
        this.goToPage(this.paginatedExpenses.pageNumber + 1);
      }
    }
  }

  // OPTIMIZE: Yeni tek API çağrısı metodu - 5 API isteği yerine 1 API isteği
  loadDashboardData(): void {
    this.isLoading = true;

    this.expenseService.getDashboardData(this.selectedYear, this.selectedMonth)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            const dashboardData = response.data;

            // Tüm verileri tek seferde ata
            this.totalDailyExpense = dashboardData.totalDailyExpense;
            this.totalMonthlyExpense = dashboardData.totalMonthlyExpense;
            this.totalYearlyExpense = dashboardData.totalYearlyExpense;
            // Eski expenses property'si kaldırıldı, artık paginatedExpenses kullanıyoruz

            // Aylık trend verisi
            const labels: string[] = [];
            const data: number[] = [];
            for (let month = 1; month <= 12; month++) {
              const label = `${month.toString().padStart(2, '0')}/${this.selectedYear}`;
              labels.push(label);
              data.push(dashboardData.monthlyExpenseSummary[month] || 0);
            }
            this.monthlyTrendData = { labels, data };

            // Filtreleri uygula ve grafikleri oluştur
            this.applyFilters();
            this.initialLoadComplete = true;
            this.createOrUpdateCharts();

          } else {
            this.handleDashboardError(response.message || 'Dashboard verileri yüklenemedi.');
          }
          this.isLoading = false;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error fetching dashboard data:', error);
          this.handleDashboardError('Dashboard verileri yüklenirken bir sunucu hatası oluştu.');
          this.isLoading = false;
          this.cdRef.detectChanges();
        }
      });
  }

  private handleDashboardError(message: string): void {
    this.toastrService.error(message, 'Hata');
    // Hata durumunda varsayılan değerleri ata
    this.totalDailyExpense = 0;
    this.totalMonthlyExpense = 0;
    this.totalYearlyExpense = 0;
    // Eski property'ler kaldırıldı, artık paginatedExpenses kullanıyoruz

    // Boş grafik verisi oluştur
    const labels: string[] = [];
    const data: number[] = [];
    for (let month = 1; month <= 12; month++) {
      labels.push(`${month.toString().padStart(2, '0')}/${this.selectedYear}`);
      data.push(0);
    }
    this.monthlyTrendData = { labels, data };

    this.createOrUpdateCharts();
  }





  // Performanslı pagination metodu (cache'li)
  loadExpensesPaginated(): void {
    this.isLoading = true;

    // Tarih değerlerini Date nesnesine çevir
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (this.filterState.startDate) {
      startDate = new Date(this.filterState.startDate);

      // Eğer başlangıç tarihi var ama bitiş tarihi yoksa, bitiş tarihi bugün olsun
      if (!this.filterState.endDate) {
        endDate = new Date(); // Bugünün tarihi
      }
    }

    if (this.filterState.endDate) {
      endDate = new Date(this.filterState.endDate);
    }

    const parameters: ExpensePagingParameters = {
      pageNumber: this.paginatedExpenses.pageNumber,
      pageSize: this.paginatedExpenses.pageSize,
      searchText: this.filterState.searchText,
      // Sıralama frontend tarafında yapılacağı için API'ye gönderilmiyor
      sortBy: 'ExpenseDate', // Default sıralama
      sortDirection: 'desc', // Default sıralama
      startDate: startDate,
      endDate: endDate,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined,
      isActive: true
    };

    // Cache key oluştur
    const cacheKey = this.generateCacheKey(parameters);

    // Cache'den kontrol et
    if (this.filterCache.has(cacheKey) && this.lastFilterParams === cacheKey) {
      const cachedData = this.filterCache.get(cacheKey)!;
      this.paginatedExpenses = cachedData;
      this.isLoading = false;
      this.cdRef.detectChanges();
      return;
    }

    this.expenseService.getExpensesPaginated(parameters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.paginatedExpenses = response.data;

            // Sıralamayı uygula
            this.applySorting();

            // Cache'e kaydet (max 10 entry)
            if (this.filterCache.size >= 10) {
              const firstKey = this.filterCache.keys().next().value;
              if (firstKey) {
                this.filterCache.delete(firstKey);
              }
            }
            this.filterCache.set(cacheKey, response.data);
            this.lastFilterParams = cacheKey;
          } else {
            this.toastrService.error(response.message || 'Giderler yüklenemedi.', 'Hata');
          }
          this.isLoading = false;
          this.isSearching = false; // Arama durumunu sıfırla
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error loading paginated expenses:', error);
          this.toastrService.error('Giderler yüklenirken bir hata oluştu.', 'Hata');
          this.isLoading = false;
          this.isSearching = false; // Arama durumunu sıfırla
          this.cdRef.detectChanges();
        }
      });
  }

  private generateCacheKey(parameters: ExpensePagingParameters): string {
    // Güvenli tarih dönüşümü
    let startDateStr = '';
    let endDateStr = '';

    try {
      if (parameters.startDate) {
        if (typeof parameters.startDate === 'string') {
          startDateStr = parameters.startDate;
        } else if (parameters.startDate instanceof Date) {
          startDateStr = parameters.startDate.toISOString();
        } else {
          startDateStr = JSON.stringify(parameters.startDate);
        }
      }

      if (parameters.endDate) {
        if (typeof parameters.endDate === 'string') {
          endDateStr = parameters.endDate;
        } else if (parameters.endDate instanceof Date) {
          endDateStr = parameters.endDate.toISOString();
        } else {
          endDateStr = JSON.stringify(parameters.endDate);
        }
      }
    } catch (error) {
      console.warn('Date conversion error in generateCacheKey:', error);
      startDateStr = parameters.startDate ? String(parameters.startDate) : '';
      endDateStr = parameters.endDate ? String(parameters.endDate) : '';
    }

    return JSON.stringify({
      pageNumber: parameters.pageNumber,
      pageSize: parameters.pageSize,
      searchText: parameters.searchText || '',
      startDate: startDateStr,
      endDate: endDateStr,
      expenseType: parameters.expenseType || '',
      minAmount: parameters.minAmount || 0,
      maxAmount: parameters.maxAmount || 0
    });
  }

  resetPaginationAndLoad(): void {
    this.paginatedExpenses.pageNumber = 1;
    this.clearCache(); // Cache'i temizle
    this.loadExpensesPaginated();
  }

  private clearCache(): void {
    this.filterCache.clear();
    this.lastFilterParams = '';
  }

  // Eski applyFilters metodu (geriye uyumluluk için)
  applyFilters(): void {
    // Bu metot artık sadece dashboard için kullanılıyor
    // Pagination için resetPaginationAndLoad kullanılıyor
    this.createOrUpdateCharts();
    this.cdRef.detectChanges();
  }








  onFilterInputChange(): void {
    // Filtre değiştiğinde sadece ara butonunu göster, otomatik arama yapma
  }

  shouldShowSearchButton(): boolean {
    // Ara butonu şu durumlarda görünür:
    // 1. Arama textbox'ında değer varsa
    // 2. Herhangi bir filtre başlangıç değerinden farklıysa
    return !!(
      this.searchControl.value ||
      this.filterState.startDate !== this.initialFilterState.startDate ||
      this.filterState.endDate !== this.initialFilterState.endDate ||
      this.filterState.expenseType !== this.initialFilterState.expenseType ||
      this.filterState.minAmount !== this.initialFilterState.minAmount ||
      this.filterState.maxAmount !== this.initialFilterState.maxAmount ||
      this.selectedYear !== this.initialYear ||
      this.selectedMonth !== this.initialMonth
    );
  }

  performSearch(): void {
    // Spam koruması - eğer zaten arama yapılıyorsa çık
    if (this.isSearching) {
      return;
    }

    const now = Date.now();
    if (now - this.lastSearchTime < this.searchCooldown) {
      this.toastrService.warning('Çok hızlı arama yapıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    this.lastSearchTime = now;
    this.isSearching = true;

    // Search control'daki değeri filterState'e aktar
    this.filterState.searchText = this.searchControl.value || '';

    // Arama işlemini başlat
    this.resetPaginationAndLoad();
  }

  clearFilters(): void {
    // Temel filtreleri temizle
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;
    this.searchControl.setValue('');
    this.isSearching = false; // Arama durumunu da sıfırla

    // Gelişmiş filtreleri başlangıç değerlerine döndür
    this.filterState = { ...this.initialFilterState };

    this.loadDashboardData();
    this.resetPaginationAndLoad();
    this.toastrService.info('Filtreler temizlendi', 'Bilgi');
  }

  clearSearch(): void {
    this.searchControl.setValue('');
    this.filterState.searchText = '';
    this.resetPaginationAndLoad();
  }

  resetFilters(): void {
    this.clearFilters();
  }

  hasActiveFilters(): boolean {
    return !!(
      this.filterState.searchText ||
      this.filterState.startDate ||
      this.filterState.endDate ||
      this.filterState.expenseType ||
      this.filterState.minAmount ||
      this.filterState.maxAmount ||
      this.selectedYear !== this.initialYear ||
      this.selectedMonth !== this.initialMonth
    );
  }

  // Sıralama metotları (frontend tarafında)
  sortBy(column: string): void {
    if (this.sortState.sortBy === column) {
      this.sortState.sortDirection = this.sortState.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortState.sortBy = column;
      this.sortState.sortDirection = 'desc';
    }

    // Frontend tarafında sıralama yap
    this.applySorting();
  }

  // Frontend tarafında sıralama uygula
  private applySorting(): void {
    if (!this.paginatedExpenses.data || this.paginatedExpenses.data.length === 0) {
      return;
    }

    const sortedData = [...this.paginatedExpenses.data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (this.sortState.sortBy) {
        case 'ExpenseDate':
          aValue = new Date(a.expenseDate);
          bValue = new Date(b.expenseDate);
          break;
        case 'ExpenseType':
          aValue = a.expenseType?.toLowerCase() || '';
          bValue = b.expenseType?.toLowerCase() || '';
          break;
        case 'Amount':
          aValue = a.amount;
          bValue = b.amount;
          break;
        case 'Description':
          aValue = a.description?.toLowerCase() || '';
          bValue = b.description?.toLowerCase() || '';
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return this.sortState.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortState.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    // Sıralanmış veriyi güncelle
    this.paginatedExpenses = {
      ...this.paginatedExpenses,
      data: sortedData
    };
  }

  getSortIcon(column: string): any {
    if (this.sortState.sortBy !== column) {
      return this.faSort;
    }
    return this.sortState.sortDirection === 'asc' ? this.faSortUp : this.faSortDown;
  }

  // Sayfalama metotları
  goToPage(page: number): void {
    if (page >= 1 && page <= this.paginatedExpenses.totalPages) {
      this.paginatedExpenses.pageNumber = page;
      this.loadExpensesPaginated();
    }
  }

  changePageSize(size: number): void {
    this.paginatedExpenses.pageSize = size;
    this.resetPaginationAndLoad();
  }

  getPageNumbers(): number[] {
    const totalPages = this.paginatedExpenses.totalPages;
    const currentPage = this.paginatedExpenses.pageNumber;
    const pages: number[] = [];

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  getMonthName(monthValue: number): string {
    return this.months.find(m => m.value === monthValue)?.name || '';
  }

  getBadgeClass(expenseType: string | null | undefined): string {
    if (!expenseType) return 'modern-badge-secondary';
    const typeLower = expenseType.toLowerCase();
    if (typeLower.includes('fatura')) return 'modern-badge-info';
    if (typeLower.includes('maaş')) return 'modern-badge-primary';
    if (typeLower.includes('kira')) return 'modern-badge-warning';
    if (typeLower.includes('malzeme')) return 'modern-badge-success';
    if (typeLower.includes('temizlik')) return 'modern-badge-info';
    if (typeLower.includes('ofis')) return 'modern-badge-secondary';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'modern-badge-danger';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'modern-badge-warning';
    return 'modern-badge-secondary';
  }

  getExpenseTypeIcon(expenseType: string | null | undefined): string {
    if (!expenseType) return 'fas fa-question-circle';
    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('elektrik')) return 'fas fa-bolt';
    if (typeLower.includes('su')) return 'fas fa-tint';
    if (typeLower.includes('doğalgaz')) return 'fas fa-fire';
    if (typeLower.includes('internet')) return 'fas fa-wifi';
    if (typeLower.includes('maaş')) return 'fas fa-user-tie';
    if (typeLower.includes('kira')) return 'fas fa-home';
    if (typeLower.includes('malzeme')) return 'fas fa-boxes';
    if (typeLower.includes('temizlik')) return 'fas fa-broom';
    if (typeLower.includes('ofis')) return 'fas fa-building';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'fas fa-tools';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'fas fa-file-invoice-dollar';
    if (typeLower.includes('diğer')) return 'fas fa-ellipsis-h';

    return 'fas fa-receipt';
  }

  getExpenseTypeTooltip(expenseType: string | null | undefined): string {
    if (!expenseType) return 'Gider türü belirtilmemiş';

    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('elektrik')) return 'Elektrik faturası gideri';
    if (typeLower.includes('su')) return 'Su faturası gideri';
    if (typeLower.includes('doğalgaz')) return 'Doğalgaz faturası gideri';
    if (typeLower.includes('internet')) return 'İnternet faturası gideri';
    if (typeLower.includes('maaş')) return 'Personel maaş ödemesi';
    if (typeLower.includes('kira')) return 'Kira ödemesi';
    if (typeLower.includes('malzeme')) return 'Malzeme alım gideri';
    if (typeLower.includes('temizlik')) return 'Temizlik malzemesi gideri';
    if (typeLower.includes('ofis')) return 'Ofis gideri';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'Bakım ve onarım gideri';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'Vergi ve harç ödemesi';

    return `${expenseType} gideri`;
  }

  getExpenseTypeColor(expenseType: string | null | undefined): string {
    if (!expenseType) return '#6c757d'; // Gray for undefined

    const typeLower = expenseType.toLowerCase();

    // Fatura giderleri - Mavi tonları
    if (typeLower.includes('elektrik')) return '#007bff';
    if (typeLower.includes('su')) return '#17a2b8';
    if (typeLower.includes('doğalgaz')) return '#fd7e14';
    if (typeLower.includes('internet')) return '#6f42c1';

    // Personel giderleri - Yeşil tonları
    if (typeLower.includes('maaş')) return '#28a745';

    // Kira - Turuncu
    if (typeLower.includes('kira')) return '#fd7e14';

    // Malzeme giderleri - Mor tonları
    if (typeLower.includes('malzeme')) return '#6f42c1';
    if (typeLower.includes('temizlik')) return '#20c997';

    // Ofis giderleri - İndigo
    if (typeLower.includes('ofis')) return '#6610f2';

    // Bakım/Onarım - Kırmızı
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return '#dc3545';

    // Vergi/Harç - Sarı
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return '#ffc107';

    // Diğer - Gri
    if (typeLower.includes('diğer')) return '#6c757d';

    // Varsayılan - Primary renk
    return '#007bff';
  }

  // Performanslı export metodu (spam korumalı)
  exportToExcel(): void {
    const now = Date.now();
    if (now - this.lastExportTime < this.exportCooldown) {
      this.toastrService.warning('Çok hızlı indirmeye çalışıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    if (this.isExporting) {
      this.toastrService.warning('Export işlemi devam ediyor...', 'Uyarı');
      return;
    }

    this.lastExportTime = now;
    this.isExporting = true;
    this.toastrService.info('Excel dosyası hazırlanıyor...', 'Bilgi');

    // Aktif filtreleri kontrol et
    const filtersActive = this.hasActiveFilters();

    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (filtersActive) {
      // Filtre varsa, filtreleme sonucunu indir
      if (this.filterState.startDate) {
        startDate = new Date(this.filterState.startDate);

        // Eğer başlangıç tarihi var ama bitiş tarihi yoksa, bitiş tarihi bugün olsun
        if (!this.filterState.endDate) {
          endDate = new Date(); // Bugünün tarihi
        }
      }

      if (this.filterState.endDate) {
        endDate = new Date(this.filterState.endDate);
      }
    } else {
      // Filtre yoksa, mevcut ayın 1'inden bugüne kadar olan verileri indir
      const now = new Date();
      startDate = new Date(now.getFullYear(), now.getMonth(), 1); // Ayın ilk günü
      endDate = new Date(); // Bugün
    }

    const parameters: ExpensePagingParameters = {
      pageNumber: 1,
      pageSize: 10000, // Tüm verileri al
      searchText: this.filterState.searchText,
      // Export için default sıralama
      sortBy: 'ExpenseDate',
      sortDirection: 'desc',
      startDate: startDate,
      endDate: endDate,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined,
      isActive: true
    };

    this.expenseService.getAllExpensesFiltered(parameters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Async olarak Excel dosyasını oluştur
            setTimeout(() => {
              this.generateExcelFile(response.data, startDate, endDate);
              this.isExporting = false;
            }, 100);
          } else {
            this.toastrService.error('Export verileri alınamadı.', 'Hata');
            this.isExporting = false;
          }
        },
        error: (error) => {
          console.error('Error exporting to Excel:', error);
          this.toastrService.error('Excel export sırasında bir hata oluştu.', 'Hata');
          this.isExporting = false;
        }
      });
  }

  private generateExcelFile(expenses: ExpenseDto[], startDate?: Date, endDate?: Date): void {
    try {
      // Yeni bir Workbook oluştur
      const workbook = new Workbook();
      const worksheet = workbook.addWorksheet('Giderler');

      // Sütun başlıklarını tanımla
      worksheet.columns = [
        { header: 'Gider Türü', key: 'expenseType', width: 25 },
        { header: 'Tutar', key: 'amount', width: 20 },
        { header: 'Gider Tarihi', key: 'expenseDate', width: 20 },
        { header: 'Açıklama', key: 'description', width: 35 },
        { header: 'Oluşturma Tarihi', key: 'creationDate', width: 20 },
        { header: '', key: 'empty1', width: 5 }, // Boş sütun
        { header: 'ÖZET', key: 'summary', width: 20 }, // Özet başlığı
        { header: 'TUTAR', key: 'summaryAmount', width: 15 } // Özet tutar
      ];

      // Veri satırlarını eklemeden önce tarihe göre sırala (en yeni en üstte)
      expenses.sort((a: ExpenseDto, b: ExpenseDto) => new Date(b.expenseDate).getTime() - new Date(a.expenseDate).getTime());

      // Export edilecek verilerden toplamları hesapla
      const exportTotals = this.calculateExpenseExportTotals(expenses);

      // Sıralanmış veriyi ekle
      let rowIndex = 2; // Başlık satırından sonra
      expenses.forEach((expense: ExpenseDto) => {
        worksheet.addRow({
          expenseType: expense.expenseType || '-',
          amount: expense.amount,
          expenseDate: new Date(expense.expenseDate).toLocaleDateString('tr-TR'),
          description: expense.description || '-',
          creationDate: new Date(expense.creationDate).toLocaleDateString('tr-TR'),
          empty1: '',
          summary: '',
          summaryAmount: ''
        });
        rowIndex++;
      });

      // Sağ tarafta özet tablosu ekle
      this.addExpenseSummaryTable(worksheet, exportTotals, rowIndex, startDate, endDate);

      // Dosya adını tarih aralığına göre oluştur
      let fileName: string;
      if (startDate && endDate) {
        const startDateStr = startDate.toLocaleDateString('tr-TR').replace(/\./g, '.');
        const endDateStr = endDate.toLocaleDateString('tr-TR').replace(/\./g, '.');
        fileName = `Giderler_${startDateStr}-${endDateStr}.xlsx`;
      } else {
        // Fallback: bugünün tarihi
        const now = new Date();
        const dateSuffix = now.toLocaleDateString('tr-TR').replace(/\./g, '.');
        fileName = `Giderler_${dateSuffix}.xlsx`;
      }

      // Workbook'u buffer'a yaz ve dosyayı indir
      workbook.xlsx.writeBuffer().then((buffer) => {
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        fs.saveAs(blob, fileName);
        this.toastrService.success('Excel dosyası başarıyla indirildi.', 'Başarılı');
      }).catch(error => {
        console.error('Excel export error:', error);
        this.toastrService.error('Excel dosyası oluşturulurken bir hata oluştu', 'Hata');
      });

    } catch (error) {
      console.error('Excel generation error:', error);
      this.toastrService.error('Excel dosyası oluşturulurken bir hata oluştu', 'Hata');
    }
  }

  // Excel export için gider toplamlarını hesapla
  private calculateExpenseExportTotals(expenses: ExpenseDto[]) {
    // Gider türlerine göre toplamları hesapla
    const expensesByType = expenses.reduce((acc, expense) => {
      const type = expense.expenseType || 'Diğer';
      if (!acc[type]) {
        acc[type] = 0;
      }
      acc[type] += expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    // Genel toplam
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    return {
      expensesByType,
      totalAmount,
      totalCount: expenses.length
    };
  }

  // Excel'e gider özet tablosu ekle
  private addExpenseSummaryTable(worksheet: any, totals: any, startRow: number, startDate?: Date, endDate?: Date) {
    // Tarih aralığını başlıkların üstüne ekle (1. satır)
    let currentRow = 1;

    if (startDate && endDate) {
      const startDateStr = startDate.toLocaleDateString('tr-TR');
      const endDateStr = endDate.toLocaleDateString('tr-TR');

      // Eğer bitiş tarihi bugünse ve sadece başlangıç tarihi seçilmişse
      const today = new Date();
      const isEndDateToday = endDate.toDateString() === today.toDateString();
      const isEndDateNotSelected = !this.filterState.endDate;

      if (isEndDateToday && isEndDateNotSelected) {
        // Bugünün tarihini göster
        const todayStr = today.toLocaleDateString('tr-TR');
        worksheet.getCell(`G${currentRow}`).value = `${startDateStr} - ${todayStr}`;
      } else {
        worksheet.getCell(`G${currentRow}`).value = `${startDateStr} - ${endDateStr}`;
      }
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    } else if (startDate && !endDate) {
      // Sadece başlangıç tarihi varsa (bu durum artık yukarıdaki mantıkla ele alınıyor)
      const startDateStr = startDate.toLocaleDateString('tr-TR');
      const todayStr = new Date().toLocaleDateString('tr-TR');
      worksheet.getCell(`G${currentRow}`).value = `${startDateStr} - ${todayStr}`;
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    } else if (!startDate && endDate) {
      // Sadece bitiş tarihi varsa
      const endDateStr = endDate.toLocaleDateString('tr-TR');
      worksheet.getCell(`G${currentRow}`).value = `Başlangıç - ${endDateStr}`;
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    } else {
      // Tarih filtresi yoksa "Tüm Veriler" yazsın
      worksheet.getCell(`G${currentRow}`).value = 'Tüm Veriler';
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    }

    // Başlık satırını formatla (2. satır)
    currentRow = 2;
    const headerRow = worksheet.getRow(currentRow);
    headerRow.getCell(7).value = 'ÖZET';
    headerRow.getCell(8).value = 'TUTAR';
    headerRow.getCell(7).font = { bold: true, size: 12 };
    headerRow.getCell(8).font = { bold: true, size: 12 };
    headerRow.getCell(7).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };
    headerRow.getCell(8).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };

    // Özet verilerini ekle (3. satırdan başla)
    currentRow = 3;

    // Gider türlerine göre toplamları ekle
    Object.entries(totals.expensesByType).forEach(([type, amount]) => {
      worksheet.getCell(`G${currentRow}`).value = type;
      worksheet.getCell(`H${currentRow}`).value = amount;
      worksheet.getCell(`H${currentRow}`).numFmt = '#,##0.00 ₺';
      currentRow++;
    });

    // Boş satır
    currentRow++;

    // Toplam gider sayısı
    worksheet.getCell(`G${currentRow}`).value = 'Toplam Gider Sayısı';
    worksheet.getCell(`H${currentRow}`).value = totals.totalCount;
    currentRow++;

    // Boş satır
    currentRow++;

    // Genel toplam
    worksheet.getCell(`G${currentRow}`).value = 'GENEL TOPLAM';
    worksheet.getCell(`H${currentRow}`).value = totals.totalAmount;
    worksheet.getCell(`H${currentRow}`).numFmt = '#,##0.00 ₺';
    worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } }; // Yeşil ve kalın
    worksheet.getCell(`H${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } };
    worksheet.getCell(`G${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };
    worksheet.getCell(`H${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };

    // Özet tablosu etrafına border ekle
    for (let row = 1; row <= currentRow; row++) {
      for (let col = 7; col <= 8; col++) {
        const cell = worksheet.getCell(row, col);
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }
    }
  }

  openExpenseDialog(expense?: ExpenseDto): void {
    const now = Date.now();
    if (now - this.lastDialogOpenTime < this.dialogCooldown) {
      return; // Sessizce engelle
    }

    this.lastDialogOpenTime = now;

    const dialogRef = this.dialog.open(ExpenseDialogComponent, {
      width: '600px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      data: expense ? { ...expense } : null,
      disableClose: false,
      panelClass: 'modern-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) { // Dialog başarıyla kapandıysa (kaydetme/güncelleme yapıldıysa)
        this.loadDashboardData();
        this.resetPaginationAndLoad();
      }
    });
  }






  deleteExpense(expense: ExpenseDto): void {
    // confirmGeneric yerine confirmExpenseDelete kullanıldı
    this.dialogService.confirmExpenseDelete(expense)
      .subscribe((result: boolean) => {
        if (result) {
          this.isLoading = true;
          this.expenseService.delete(expense.expenseID)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (response) => {
                if (response.success) {
                  this.toastrService.success('Gider başarıyla silindi.', 'Başarılı');
                  this.loadDashboardData();
                  this.resetPaginationAndLoad();
                } else {
                  this.toastrService.error(response.message || 'Gider silinemedi.', 'Hata');
                   this.isLoading = false;
                }
              },
              error: (error) => {
                console.error('Error deleting expense:', error);
                this.toastrService.error('Gider silinirken bir sunucu hatası oluştu.', 'Hata');
                this.isLoading = false;
              }
            });
        }
      });
  }

  // --- Chart Methods ---

  destroyCharts(): void {
    this.distributionChart?.destroy();
    this.trendChart?.destroy();
    this.distributionChart = null;
    this.trendChart = null;
  }

  createOrUpdateCharts(): void {
     if (!this.initialLoadComplete || typeof document === 'undefined') return;
     this.destroyCharts();
     this.createDistributionChart();
     this.createTrendChart();
     this.cdRef.detectChanges();
  }



  createDistributionChart(): void {
    const canvas = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    if (!canvas) return;

    // Pagination verilerini kullan
    const expensesToAnalyze = this.paginatedExpenses.data.length > 0 ? this.paginatedExpenses.data : [];

    if (expensesToAnalyze.length === 0) {
      this.createEmptyDistributionChart();
      return;
    }

    const typeData = expensesToAnalyze.reduce((acc, expense) => {
      const type = expense.expenseType || 'Diğer';
      acc[type] = (acc[type] || 0) + expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    const labels = Object.keys(typeData);
    const data = Object.values(typeData);

    if (labels.length === 0 || data.every(value => value === 0)) {
      this.createEmptyDistributionChart();
      return;
    }

    // ChartUtils service'ini kullan
    const chartData = {
      labels: labels,
      values: data
    };

    try {
      // Mevcut chart'ı temizle
      if (this.distributionChart) {
        this.distributionChart.destroy();
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(canvas);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning distribution chart from registry:', e);
      }

      const chartConfig = this.chartUtils.getExpenseDistributionConfig(chartData) as ChartConfiguration;
      this.distributionChart = new Chart(canvas, chartConfig);
    } catch (error) {
      console.error('Error creating distribution chart:', error);
      this.createEmptyDistributionChart();
    }
  }

  private createEmptyDistributionChart(): void {
    const canvas = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    if (!canvas) return;

    try {
      // Mevcut chart'ı temizle
      if (this.distributionChart) {
        this.distributionChart.destroy();
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(canvas);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning empty distribution chart from registry:', e);
      }

      // Boş veri ile ChartUtils kullan
      const emptyData = {
        labels: ['Veri Yok'],
        values: [1]
      };

      const chartConfig = this.chartUtils.getExpenseDistributionConfig(emptyData) as ChartConfiguration;

      // Boş chart için özel ayarlar
      if (chartConfig.options && chartConfig.options.plugins) {
        chartConfig.options.plugins.legend = { display: false };
        chartConfig.options.plugins.tooltip = { enabled: false };
      }

      // Boş chart için gri renk
      if (chartConfig.data && chartConfig.data.datasets && chartConfig.data.datasets[0]) {
        chartConfig.data.datasets[0].backgroundColor = ['rgba(108, 117, 125, 0.3)'];
        chartConfig.data.datasets[0].borderColor = ['rgba(108, 117, 125, 0.5)'];
      }

      this.distributionChart = new Chart(canvas, chartConfig);

      // Canvas üzerine metin ekle
      const ctx = canvas.getContext('2d');
      if (ctx) {
        this.addEmptyStateText(ctx, 'Gösterilecek gider verisi yok');
      }
    } catch (error) {
      console.error('Error creating empty distribution chart:', error);
    }
  }

  private createEmptyTrendChart(): void {
    const canvas = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
    if (!canvas) return;

    try {
      // Mevcut chart'ı temizle
      if (this.trendChart) {
        this.trendChart.destroy();
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(canvas);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning empty trend chart from registry:', e);
      }

      // Boş veri ile ChartUtils kullan
      const emptyData = {
        monthlyExpense: Array(12).fill(0)
      };

      const chartConfig = this.chartUtils.getMonthlyExpenseConfig(emptyData) as ChartConfiguration;

      // Boş chart için özel ayarlar
      if (chartConfig.options && chartConfig.options.plugins) {
        chartConfig.options.plugins.legend = { display: false };
        chartConfig.options.plugins.tooltip = { enabled: false };
      }

      if (chartConfig.options && chartConfig.options.scales) {
        chartConfig.options.scales.y = { beginAtZero: true, display: false };
        chartConfig.options.scales.x = { display: false };
      }

      // Boş chart için gri renk
      if (chartConfig.data && chartConfig.data.datasets && chartConfig.data.datasets[0]) {
        chartConfig.data.datasets[0].backgroundColor = 'rgba(108, 117, 125, 0.1)';
        chartConfig.data.datasets[0].borderColor = 'rgba(108, 117, 125, 0.5)';
        (chartConfig.data.datasets[0] as any).pointRadius = 0;
      }

      this.trendChart = new Chart(canvas, chartConfig);

      // Canvas üzerine metin ekle
      const ctx = canvas.getContext('2d');
      if (ctx) {
        this.addEmptyStateText(ctx, 'Gösterilecek trend verisi yok');
      }
    } catch (error) {
      console.error('Error creating empty trend chart:', error);
    }
  }

  private addEmptyStateText(ctx: CanvasRenderingContext2D, text: string): void {
    const canvas = ctx.canvas;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = '16px Arial';
    ctx.fillStyle = 'rgba(108, 117, 125, 0.7)';
    ctx.fillText(text, centerX, centerY);
    ctx.restore();
  }

  createTrendChart(): void {
    const canvas = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
    if (!canvas) return;

    // Aylık trend verisini kontrol et
    const labels = this.monthlyTrendData.labels;
    const data = this.monthlyTrendData.data;

    if (!labels.length || !data.length || data.every(value => value === 0)) {
      this.createEmptyTrendChart();
      return;
    }

    // ChartUtils service'ini kullan
    const chartData = {
      monthlyExpense: data
    };

    try {
      // Mevcut chart'ı temizle
      if (this.trendChart) {
        this.trendChart.destroy();
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(canvas);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning trend chart from registry:', e);
      }

      const chartConfig = this.chartUtils.getMonthlyExpenseConfig(chartData) as ChartConfiguration;
      this.trendChart = new Chart(canvas, chartConfig);
    } catch (error) {
      console.error('Error creating trend chart:', error);
      this.createEmptyTrendChart();
    }
  }



  // Trend göstergeleri kaldırıldı





  // Grafik etkileşim metotları
  onChartClick(event: any, chart: Chart): void {
    const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);

    if (points.length) {
      const firstPoint = points[0];
      const label = chart.data.labels?.[firstPoint.index];
      const value = chart.data.datasets[firstPoint.datasetIndex].data[firstPoint.index];

      // Tıklanan veri noktası hakkında detay göster
      this.toastrService.info(`${label}: ${new Intl.NumberFormat('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value as number)} ₺`, 'Grafik Detayı');
    }
  }

  // Grafik hover efektleri
  onChartHover(event: any, chart: Chart): void {
    const canvas = chart.canvas;
    if (canvas) {
      canvas.style.cursor = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true).length > 0 ? 'pointer' : 'default';
    }
  }

  // Favori filtre metotları kaldırıldı





  // Hızlı filtreler kaldırıldı
}