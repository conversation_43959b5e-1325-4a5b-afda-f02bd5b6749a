import { Injectable } from '@angular/core';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';

@Injectable({
  providedIn: 'root'
})
export class ChartUtilsService {

  constructor() { }

  /**
   * Get configuration for payment statistics chart
   * @param data Object containing payment totals
   * @returns Chart configuration
   */
  getPaymentStatsConfig(data: any): ChartConfiguration {
    const chartData: ChartData = {
      labels: ['Nakit', '<PERSON><PERSON><PERSON>', 'Havale/EFT', 'Borç'],
      datasets: [
        {
          data: [data.totalCash, data.totalCreditCard, data.totalTransfer, data.totalDebt],
          backgroundColor: [
            'rgba(40, 167, 69, 0.7)',  // Green for cash
            'rgba(0, 123, 255, 0.7)',  // Blue for credit card
            'rgba(255, 193, 7, 0.7)',  // Yellow for transfer
            'rgba(220, 53, 69, 0.7)'   // Red for debt
          ],
          borderColor: [
            'rgb(40, 167, 69)',
            'rgb(0, 123, 255)',
            'rgb(255, 193, 7)',
            'rgb(220, 53, 69)'
          ],
          borderWidth: 1
        }
      ]
    };

    // In Chart.js v4, we need to specify the chart type and configuration differently
    const config: any = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle'
            }
          },
          tooltip: {
            callbacks: {
              label: function(context: any): string {
                const label = context.label || '';
                const value = context.raw as number;
                const total = context.dataset.data.reduce(
                  (a: number, b: number) => a + b, 
                  0
                );
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value.toLocaleString('tr-TR')} ₺ (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        }
      }
    };
    
    // Add cutout percentage - this is now handled differently in Chart.js v4
    config.options.radius = '90%';
    config.options.cutout = '70%';
    
    return config;
  }

  /**
   * Get configuration for monthly revenue chart
   * @param data Object containing monthly revenue data
   * @returns Chart configuration
   */
  getMonthlyRevenueConfig(data: any): ChartConfiguration {
    const months = [
      'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
      'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
    ];

    // Trend hesaplama fonksiyonu
    const calculateTrendData = (data: number[]): Array<{
      percentage: number;
      difference: number;
      isIncrease: boolean;
      isDecrease: boolean;
    }> => {
      const trends = [];

      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          trends.push({ percentage: 0, difference: 0, isIncrease: false, isDecrease: false });
          continue;
        }

        const current = data[i];
        const previous = data[i - 1];
        const difference = current - previous;

        let percentage = 0;
        if (previous !== 0) {
          percentage = (difference / previous) * 100;
        } else if (current > 0) {
          percentage = 100; // Önceki ay 0, bu ay pozitif
        }

        trends.push({
          percentage,
          difference,
          isIncrease: difference > 0,
          isDecrease: difference < 0
        });
      }

      return trends;
    };

    // Trend verilerini hesapla
    const trendData = calculateTrendData(data.monthlyRevenue);

    const chartData: ChartData = {
      labels: months,
      datasets: [
        {
          type: 'line' as ChartType,
          label: 'Aylık Gelir',
          data: data.monthlyRevenue,
          backgroundColor: 'rgba(67, 97, 238, 0.2)',
          borderColor: 'rgb(67, 97, 238)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: 'rgb(67, 97, 238)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6
        }
      ]
    };

    // For the monthly revenue chart
    const config: any = {
      type: 'line' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value: any): string {
                return value.toLocaleString('tr-TR') + ' ₺';
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: 'rgb(67, 97, 238)',
            borderWidth: 1,
            callbacks: {
              label: function(context: any): string {
                const label = context.dataset.label || '';
                const value = context.raw as number;
                if (label) {
                  return `${label}: ${value.toLocaleString('tr-TR')} ₺`;
                }
                return `${value.toLocaleString('tr-TR')} ₺`;
              },
              afterLabel: function(context: any): string {
                const monthIndex = context.dataIndex;
                if (monthIndex > 0 && trendData[monthIndex]) {
                  const trend = trendData[monthIndex];
                  const percentageText = trend.percentage >= 0 ?
                    `+${trend.percentage.toFixed(1)}%` :
                    `${trend.percentage.toFixed(1)}%`;
                  const differenceText = new Intl.NumberFormat('tr-TR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }).format(Math.abs(trend.difference));

                  return `Önceki aya göre: ${percentageText} (${trend.isIncrease ? '+' : '-'}${differenceText} ₺)`;
                }
                return '';
              }
            }
          }
        },
        hover: {
          mode: 'nearest',
          intersect: true
        },
        interaction: {
          intersect: false,
          mode: 'index'
        },
        animation: {
          duration: 1000,
          easing: 'easeInOutQuad'
        }
      }
    };

    return config;
  }

  /**
   * Get configuration for expense distribution chart (doughnut chart)
   * @param data Object containing expense type data
   * @returns Chart configuration
   */
  getExpenseDistributionConfig(data: any): ChartConfiguration {
    const chartData: ChartData = {
      labels: data.labels,
      datasets: [
        {
          data: data.values,
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',   // Kırmızı
            'rgba(54, 162, 235, 0.7)',   // Mavi
            'rgba(255, 205, 86, 0.7)',   // Sarı
            'rgba(75, 192, 192, 0.7)',   // Turkuaz
            'rgba(153, 102, 255, 0.7)',  // Mor
            'rgba(255, 159, 64, 0.7)',   // Turuncu
            'rgba(199, 199, 199, 0.7)',  // Gri
            'rgba(83, 102, 255, 0.7)'    // İndigo
          ],
          borderColor: [
            'rgb(255, 99, 132)',
            'rgb(54, 162, 235)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(153, 102, 255)',
            'rgb(255, 159, 64)',
            'rgb(199, 199, 199)',
            'rgb(83, 102, 255)'
          ],
          borderWidth: 2,
          hoverOffset: 12,
          hoverBorderWidth: 3
        }
      ]
    };

    const config: any = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle'
            }
          },
          tooltip: {
            callbacks: {
              label: function(context: any): string {
                const label = context.label || '';
                const value = context.raw as number;
                const total = context.dataset.data.reduce(
                  (a: number, b: number) => a + b,
                  0
                );
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value.toLocaleString('tr-TR')} ₺ (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        }
      }
    };

    config.options.radius = '90%';
    config.options.cutout = '70%';

    return config;
  }

  /**
   * Get configuration for monthly expense trend chart
   * @param data Object containing monthly expense data
   * @returns Chart configuration
   */
  getMonthlyExpenseConfig(data: any): ChartConfiguration {
    const months = [
      'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
      'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
    ];

    // Trend hesaplama fonksiyonu
    const calculateTrendData = (data: number[]): Array<{
      percentage: number;
      difference: number;
      isIncrease: boolean;
      isDecrease: boolean;
    }> => {
      const trends = [];

      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          trends.push({ percentage: 0, difference: 0, isIncrease: false, isDecrease: false });
          continue;
        }

        const current = data[i];
        const previous = data[i - 1];
        const difference = current - previous;

        let percentage = 0;
        if (previous !== 0) {
          percentage = (difference / previous) * 100;
        } else if (current > 0) {
          percentage = 100; // Önceki ay 0, bu ay pozitif
        }

        trends.push({
          percentage,
          difference,
          isIncrease: difference > 0,
          isDecrease: difference < 0
        });
      }

      return trends;
    };

    // Trend verilerini hesapla
    const trendData = calculateTrendData(data.monthlyExpense);

    const chartData: ChartData = {
      labels: months,
      datasets: [
        {
          type: 'line' as ChartType,
          label: 'Aylık Gider',
          data: data.monthlyExpense,
          backgroundColor: 'rgba(220, 53, 69, 0.2)',
          borderColor: 'rgb(220, 53, 69)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: 'rgb(220, 53, 69)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6
        }
      ]
    };

    const config: any = {
      type: 'line' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value: any): string {
                return value.toLocaleString('tr-TR') + ' ₺';
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: 'rgb(220, 53, 69)',
            borderWidth: 1,
            callbacks: {
              label: function(context: any): string {
                const label = context.dataset.label || '';
                const value = context.raw as number;
                if (label) {
                  return `${label}: ${value.toLocaleString('tr-TR')} ₺`;
                }
                return `${value.toLocaleString('tr-TR')} ₺`;
              },
              afterLabel: function(context: any): string {
                const monthIndex = context.dataIndex;
                if (monthIndex > 0 && trendData[monthIndex]) {
                  const trend = trendData[monthIndex];
                  const percentageText = trend.percentage >= 0 ?
                    `+${trend.percentage.toFixed(1)}%` :
                    `${trend.percentage.toFixed(1)}%`;
                  const differenceText = new Intl.NumberFormat('tr-TR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }).format(Math.abs(trend.difference));

                  return `Önceki aya göre: ${percentageText} (${trend.isIncrease ? '+' : '-'}${differenceText} ₺)`;
                }
                return '';
              }
            }
          }
        },
        hover: {
          mode: 'nearest',
          intersect: true
        },
        interaction: {
          intersect: false,
          mode: 'index'
        },
        animation: {
          duration: 1000,
          easing: 'easeInOutQuad'
        }
      }
    };

    return config;
  }
}
